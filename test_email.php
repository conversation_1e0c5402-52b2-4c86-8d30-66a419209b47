<?php
require "PHPMailer/src/Exception.php"; 
require "PHPMailer/src/PHPMailer.php"; 
require "PHPMailer/src/SMTP.php"; 

use PHPMailer\PHPMailer\PHPMailer; 
use P<PERSON>Mailer\PHPMailer\Exception;

$mail = new PHPMailer(true);

try {
    // SMTP configuration
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'jydxgtldijspckyp';
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    
    // Enable debugging
    $mail->SMTPDebug = 2;
    
    $mail->setFrom('<EMAIL>', 'GPS Service Test');
    $mail->addAddress('<EMAIL>', 'Admin');
    
    $mail->Subject = 'Test Email from GPS Service';
    $mail->Body = 'This is a test email to verify the email configuration is working properly.';
    
    $mail->send();
    echo 'Test email sent successfully!';
} catch (Exception $e) {
    echo "Test email failed. Error: {$mail->ErrorInfo}";
}
?> 
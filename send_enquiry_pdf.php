<?php 
require('fpdf/fpdf.php'); 
require "PHPMailer/src/Exception.php"; 
require "PHPMailer/src/PHPMailer.php"; 
require "PHPMailer/src/SMTP.php"; 

use PHPMailer\PHPMailer\PHPMailer; 
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: index.php?error=" . urlencode("Invalid request method"));
    exit;
}

// Collect form data
$data = $_POST;

// Debug: Log received data
error_log("Form data received: " . print_r($data, true));

// Validate required fields
$required_fields = ['serviceType', 'serviceLocation', 'serviceDate', 'companyName', 'companyAddress', 'contactName', 'designation', 'contactNumber', 'emailAddress'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    error_log("Missing required fields: " . implode(', ', $missing_fields));
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Please fill all required fields: ' . implode(', ', $missing_fields)
    ]);
    exit;
}

// Validate email format
if (!filter_var($data['emailAddress'], FILTER_VALIDATE_EMAIL)) {
    error_log("Invalid email format: " . $data['emailAddress']);
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Please enter a valid email address'
    ]);
    exit;
}

// Handle file upload
$visitingCardPath = '';
if(isset($_FILES['visitingCardFile']) && $_FILES['visitingCardFile']['error'] == UPLOAD_ERR_OK) {
    $uploadDir = 'uploads/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    $visitingCardPath = $uploadDir . basename($_FILES['visitingCardFile']['name']);
    move_uploaded_file($_FILES['visitingCardFile']['tmp_name'], $visitingCardPath);
}

// Generate PDF
$pdf = new FPDF();
$pdf->AddPage();
$pdf->SetMargins(20, 20, 20);

// Header with border
$pdf->SetFont('Arial','B',18);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(0,15,'GPS SERVICE PVT. LTD.',1,1,'C',true);
$pdf->Ln(5);

$pdf->SetFont('Arial','B',14);
$pdf->SetFillColor(250, 250, 250);
$pdf->Cell(0,10,'SERVICE ENQUIRY DETAILS',1,1,'C',true);
$pdf->Ln(10);

// Service Section with borders
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'SERVICE DETAILS',1,1,'L',true);
$pdf->SetFont('Arial','',10);

// Service details in a table format
$pdf->Cell(50,8,'Service Type:',1,0,'L');
$pdf->Cell(0,8,($data['serviceType'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Location:',1,0,'L');
$pdf->Cell(0,8,($data['serviceLocation'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Required From:',1,0,'L');
$pdf->Cell(0,8,($data['serviceDate'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Duration:',1,0,'L');
$pdf->Cell(0,8,($data['serviceDuration'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Personnel Count:',1,0,'L');
$pdf->Cell(0,8,'Male: '.($data['malePersons'] ?? '0').', Female: '.($data['femalePersons'] ?? '0').', Total: '.($data['totalPersons'] ?? '0'),1,1,'L');

$pdf->Cell(50,8,'Working Hours:',1,0,'L');
$pdf->Cell(0,8,($data['workingHours'] ?? 'Not specified'),1,1,'L');

$pdf->Ln(5);

// Additional Service Items
if(isset($data['additionalServiceType']) && !empty($data['additionalServiceType'][0])){
    $pdf->SetFont('Arial','B',12);
    $pdf->SetFillColor(230, 230, 230);
    $pdf->Cell(0,8,'ADDITIONAL SERVICES',1,1,'L',true);
    $pdf->SetFont('Arial','',10);
    
    foreach($data['additionalServiceType'] as $i => $stype){
        if(!empty($stype)) {
            $pdf->SetFont('Arial','B',10);
            $pdf->Cell(0,8,'Service '.($i+1),1,1,'L');
            $pdf->SetFont('Arial','',10);
            
            $pdf->Cell(50,6,'Service Type:',1,0,'L');
            $pdf->Cell(0,6,$stype,1,1,'L');
            
            $pdf->Cell(50,6,'Purpose:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalServicePurpose'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Cell(50,6,'Start Date:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalStartDate'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Cell(50,6,'End Date:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalEndDate'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Cell(50,6,'Personnel:',1,0,'L');
            $pdf->Cell(0,6,'Male: '.($data['additionalMalePersons'][$i] ?? '0').', Female: '.($data['additionalFemalePersons'][$i] ?? '0').', Total: '.($data['additionalTotalPersons'][$i] ?? '0'),1,1,'L');
            
            $pdf->Ln(3);
        }
    }
    $pdf->Ln(5);
}

// Company Info
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'COMPANY INFORMATION',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Company Name:',1,0,'L');
$pdf->Cell(0,8,($data['companyName'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Company Address:',1,0,'L');
$pdf->Cell(0,8,($data['companyAddress'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'GST Status:',1,0,'L');
$pdf->Cell(0,8,($data['gstStatus'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'GST Number:',1,0,'L');
$pdf->Cell(0,8,($data['gstNumber'] ?? 'Not provided'),1,1,'L');

$pdf->Ln(5);

// Contact Details
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'PRIMARY CONTACT DETAILS',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Name:',1,0,'L');
$pdf->Cell(0,8,($data['contactName'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Designation:',1,0,'L');
$pdf->Cell(0,8,($data['designation'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Contact Number:',1,0,'L');
$pdf->Cell(0,8,($data['contactNumber'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Email Address:',1,0,'L');
$pdf->Cell(0,8,($data['emailAddress'] ?? 'Not specified'),1,1,'L');

$pdf->Ln(5);

// Additional Contacts
if(isset($data['additionalContactName']) && !empty($data['additionalContactName'][0])){
    $pdf->SetFont('Arial','B',12);
    $pdf->SetFillColor(230, 230, 230);
    $pdf->Cell(0,8,'ADDITIONAL CONTACTS',1,1,'L',true);
    $pdf->SetFont('Arial','',10);
    
    foreach($data['additionalContactName'] as $i => $cname){
        if(!empty($cname)) {
            $pdf->SetFont('Arial','B',10);
            $pdf->Cell(0,8,'Contact '.($i+1),1,1,'L');
            $pdf->SetFont('Arial','',10);
            
            $pdf->Cell(50,6,'Name:',1,0,'L');
            $pdf->Cell(0,6,$cname,1,1,'L');
            
            $pdf->Cell(50,6,'Designation:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalDesignation'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Cell(50,6,'Contact Number:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalContactNumber'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Cell(50,6,'Email Address:',1,0,'L');
            $pdf->Cell(0,6,($data['additionalEmailAddress'][$i] ?? 'Not specified'),1,1,'L');
            
            $pdf->Ln(3);
        }
    }
    $pdf->Ln(5);
}

// Appointment Details
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'APPOINTMENT SCHEDULE',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Appointment Date:',1,0,'L');
$pdf->Cell(0,8,($data['appointmentDate'] ?? 'Not scheduled'),1,1,'L');

$pdf->Cell(50,8,'Time Slot:',1,0,'L');
$fromTime = ($data['fromTime'] ?? '') ? $data['fromTime'].':00' : 'Not specified';
$toTime = ($data['toTime'] ?? '') ? $data['toTime'].':00' : 'Not specified';
$pdf->Cell(0,8,'From: '.$fromTime.' To: '.$toTime,1,1,'L');

// Add footer
$pdf->Ln(10);
$pdf->SetFont('Arial','I',8);
$pdf->Cell(0,6,'Generated on: '.date('Y-m-d H:i:s'),0,1,'R');
$pdf->Cell(0,6,'GPS Service Pvt. Ltd. - Professional Security & Facility Management Services',0,1,'C');

// Save PDF to temp file
$pdfFile = tempnam(sys_get_temp_dir(), 'enquiry_').'.pdf';
$pdf->Output('F', $pdfFile);

// Send Email
$mail = new PHPMailer(true);
try {
    // SMTP configuration
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'jydxgtldijspckyp';
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    
    // Enable debugging (remove in production)
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = 'error_log';
    
    $mail->setFrom('<EMAIL>', 'GPS Service Website');
    $mail->addAddress('<EMAIL>', 'GPS Service Admin');
    $mail->addReplyTo($data['emailAddress'] ?? '<EMAIL>', $data['contactName'] ?? 'Enquiry');
    
    $mail->Subject = 'New Service Enquiry from ' . ($data['companyName'] ?? 'Unknown Company');
    $mail->Body = "A new service enquiry has been submitted. Please see the attached PDF for details.\n\n";
    $mail->Body .= "Company: " . ($data['companyName'] ?? '') . "\n";
    $mail->Body .= "Contact: " . ($data['contactName'] ?? '') . "\n";
    $mail->Body .= "Email: " . ($data['emailAddress'] ?? '') . "\n";
    $mail->Body .= "Phone: " . ($data['contactNumber'] ?? '') . "\n";
    
    $mail->addAttachment($pdfFile, 'EnquiryDetails.pdf');
    if($visitingCardPath && file_exists($visitingCardPath)) {
        $mail->addAttachment($visitingCardPath);
    }
    
    $mail->send();
    error_log("Email sent successfully!");
    
    // Clean up
    unlink($pdfFile);
    if($visitingCardPath && file_exists($visitingCardPath)) {
        unlink($visitingCardPath);
    }
    
    // Return success response for AJAX
    echo json_encode([
        'success' => true,
        'message' => 'Enquiry submitted successfully!'
    ]);
    exit;
} catch (Exception $e) {
    // Clean up even if email fails
    if(file_exists($pdfFile)) {
        unlink($pdfFile);
    }
    if($visitingCardPath && file_exists($visitingCardPath)) {
        unlink($visitingCardPath);
    }
    
    // Log the error
    error_log("Mailer Error: " . $mail->ErrorInfo);
    error_log("Exception: " . $e->getMessage());
    
    // Return error response for AJAX
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to send email. Please try again later.',
        'error' => $e->getMessage()
    ]);
    exit;
}
?>
<?php 
require('fpdf/fpdf.php'); 
require "PHPMailer/src/Exception.php"; 
require "PHPMailer/src/PHPMailer.php"; 
require "PHPMailer/src/SMTP.php"; 

use PHPMailer\PHPMailer\PHPMailer; 
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: index.php?error=" . urlencode("Invalid request method"));
    exit;
}

// Collect form data
$data = $_POST;

// Debug: Log received data
error_log("Job application data received: " . print_r($data, true));

// Validate required fields
$required_fields = ['jobRole', 'jobFullName', 'jobPhoneNumber'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    error_log("Missing required fields: " . implode(', ', $missing_fields));
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Please fill all required fields: ' . implode(', ', $missing_fields)
    ]);
    exit;
}

// Validate email format if provided
if (!empty($data['jobEmailAddress']) && !filter_var($data['jobEmailAddress'], FILTER_VALIDATE_EMAIL)) {
    error_log("Invalid email format: " . $data['jobEmailAddress']);
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Please enter a valid email address'
    ]);
    exit;
}

// Generate PDF
$pdf = new FPDF();
$pdf->AddPage();
$pdf->SetMargins(20, 20, 20);

// Header with border
$pdf->SetFont('Arial','B',18);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(0,15,'GPS SERVICE PVT. LTD.',1,1,'C',true);
$pdf->Ln(5);

$pdf->SetFont('Arial','B',14);
$pdf->SetFillColor(250, 250, 250);
$pdf->Cell(0,10,'JOB APPLICATION FORM',1,1,'C',true);
$pdf->Ln(10);

// Personal Information Section
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'PERSONAL INFORMATION',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Job Role:',1,0,'L');
$pdf->Cell(0,8,ucwords(str_replace('-', ' ', $data['jobRole'] ?? 'Not specified')),1,1,'L');

$pdf->Cell(50,8,'Full Name:',1,0,'L');
$pdf->Cell(0,8,($data['jobFullName'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Father Name:',1,0,'L');
$pdf->Cell(0,8,($data['jobFatherName'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Mother Name:',1,0,'L');
$pdf->Cell(0,8,($data['jobMotherName'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Date of Birth:',1,0,'L');
$pdf->Cell(0,8,($data['jobDob'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Gender:',1,0,'L');
$pdf->Cell(0,8,ucfirst($data['jobGender'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Marital Status:',1,0,'L');
$pdf->Cell(0,8,ucfirst($data['jobMaritalStatus'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Blood Group:',1,0,'L');
$pdf->Cell(0,8,($data['jobBloodGroup'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Religion:',1,0,'L');
$pdf->Cell(0,8,ucfirst($data['jobReligion'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Height (cm):',1,0,'L');
$pdf->Cell(0,8,($data['jobHeight'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Weight (kg):',1,0,'L');
$pdf->Cell(0,8,($data['jobWeight'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Identification Marks:',1,0,'L');
$pdf->Cell(0,8,($data['jobIdentificationMarks'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Aadhar Number:',1,0,'L');
$pdf->Cell(0,8,($data['jobAadharNumber'] ?? 'Not provided'),1,1,'L');

$pdf->Ln(5);

// Address Information Section
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'ADDRESS INFORMATION',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Village/House No.:',1,0,'L');
$pdf->Cell(0,8,($data['jobVillageHouseNo'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Post Office:',1,0,'L');
$pdf->Cell(0,8,($data['jobPostOffice'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Block/Municipality:',1,0,'L');
$pdf->Cell(0,8,($data['jobBlockMunicipality'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Police Station:',1,0,'L');
$pdf->Cell(0,8,($data['jobPoliceStation'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'District:',1,0,'L');
$pdf->Cell(0,8,($data['jobDistrict'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'State:',1,0,'L');
$pdf->Cell(0,8,ucwords(str_replace('-', ' ', $data['jobState'] ?? 'Not specified')),1,1,'L');

$pdf->Cell(50,8,'Zip Code:',1,0,'L');
$pdf->Cell(0,8,($data['jobZipCode'] ?? 'Not specified'),1,1,'L');

$pdf->Ln(5);

// Contact Information Section
$pdf->SetFont('Arial','B',12);
$pdf->SetFillColor(230, 230, 230);
$pdf->Cell(0,8,'CONTACT INFORMATION',1,1,'L',true);
$pdf->SetFont('Arial','',10);

$pdf->Cell(50,8,'Phone Number:',1,0,'L');
$pdf->Cell(0,8,($data['jobPhoneNumber'] ?? 'Not specified'),1,1,'L');

$pdf->Cell(50,8,'Alternative Number:',1,0,'L');
$pdf->Cell(0,8,($data['jobAlternativeNumber'] ?? 'Not provided'),1,1,'L');

$pdf->Cell(50,8,'Email Address:',1,0,'L');
$pdf->Cell(0,8,($data['jobEmailAddress'] ?? 'Not provided'),1,1,'L');

// Add footer
$pdf->Ln(10);
$pdf->SetFont('Arial','I',8);
$pdf->Cell(0,6,'Application submitted on: '.date('Y-m-d H:i:s'),0,1,'R');
$pdf->Cell(0,6,'GPS Service Pvt. Ltd. - Professional Security & Facility Management Services',0,1,'C');

// Save PDF to temp file
$pdfFile = tempnam(sys_get_temp_dir(), 'job_application_').'.pdf';
$pdf->Output('F', $pdfFile);

// Email configuration
$smtp_host = 'smtp.gmail.com';
$smtp_username = '<EMAIL>';
$smtp_password = 'jydxgtldijspckyp';
$admin_email = '<EMAIL>';

// Send Email
$mail = new PHPMailer(true);
try {
    // SMTP configuration
    $mail->isSMTP();
    $mail->Host = $smtp_host;
    $mail->SMTPAuth = true;
    $mail->Username = $smtp_username;
    $mail->Password = $smtp_password;
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    
    // Enable debugging (remove in production)
    $mail->SMTPDebug = 0; // Disabled for clean JSON output
    $mail->Debugoutput = 'error_log';
    
    $mail->setFrom($smtp_username, 'GPS Service Website');
    $mail->addAddress($admin_email, 'GPS Service Admin');
    $mail->addReplyTo($data['jobEmailAddress'] ?? '<EMAIL>', $data['jobFullName'] ?? 'Job Applicant');
    
    $mail->Subject = 'New Job Application for ' . ucwords(str_replace('-', ' ', $data['jobRole'] ?? 'Unknown Position'));
    $mail->Body = "A new job application has been submitted. Please see the attached PDF for details.\n\n";
    $mail->Body .= "Position: " . ucwords(str_replace('-', ' ', $data['jobRole'] ?? '')) . "\n";
    $mail->Body .= "Name: " . ($data['jobFullName'] ?? '') . "\n";
    $mail->Body .= "Phone: " . ($data['jobPhoneNumber'] ?? '') . "\n";
    $mail->Body .= "Email: " . ($data['jobEmailAddress'] ?? 'Not provided') . "\n";
    
    $mail->addAttachment($pdfFile, 'JobApplication_' . preg_replace('/[^A-Za-z0-9_-]/', '_', $data['jobFullName'] ?? 'Unknown') . '.pdf');
    
    $mail->send();
    error_log("Job application email sent successfully!");

    // Clean up
    unlink($pdfFile);

    // Redirect to index.php with success message
    header("Location: index.php?job_success=1&message=" . urlencode('Job application submitted successfully!'));
    exit;
} catch (Exception $e) {
    // Clean up even if email fails
    if(file_exists($pdfFile)) {
        unlink($pdfFile);
    }

    // Log the error
    error_log("Mailer Error: " . $mail->ErrorInfo);
    error_log("Exception: " . $e->getMessage());

    // Redirect to index.php with error message
    header("Location: index.php?job_success=0&message=" . urlencode('Failed to send job application. Please try again later.'));
    exit;
}
?>